name: UltimateLogger
version: '${version}'
main: ca.xef5000.ultimateLogger.UltimateLogger
api-version: '1.20'

commands:
  logger:
    description: Main command for the UltimateLogger plugin.
    usage: "/<command> [view|stats|reload|help]"
    aliases: [ul, ultlog]

permissions:
  ultimatelogger.view:
    description: Allows viewing logs
    default: op
  ultimatelogger.stats:
    description: Allows viewing plugin statistics
    default: op
  ultimatelogger.reload:
    description: Allows reloading the plugin configuration
    default: op
  ultimatelogger.archive:
    description: Allows archiving logs to protect them from auto-deletion
    default: op
  ultimatelogger.unarchive:
    description: Allows unarchiving logs to make them expire normally
    default: op
  ultimatelogger.delete:
    description: Allows deleting logs from the database
    default: op
  ultimatelogger.clear:
    description: Allows manual cleaning of the database
    default: op
