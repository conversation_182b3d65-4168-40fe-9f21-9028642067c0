# UltimateLogger Configuration

# Database settings
# type: can be "SQLITE" or "MYSQL"
database:
  type: "SQLITE"

  # Settings for SQLite
  sqlite:
    # The name of the database file.
    # It will be created in the UltimateLogger plugin folder.
    filename: "logs.db"

  # Settings for MySQL
  mysql:
    host: "localhost"
    port: 3306
    database: "ultimatelogger"
    username: "user"
    password: "password"

  # Connection pool settings
  pool:
    # Maximum number of database connections in the pool
    max-size: 10

# Log Manager settings
logs:
  # Cache settings
  cache:
    # Maximum number of pages to store in the cache
    max-size: 100
    # Time in minutes after which cache entries expire if not accessed
    expiry-minutes: 5

  # Batch processing settings
  batch:
    # Maximum number of logs to process in a single batch
    size: 100
    # Interval in ticks between batch processing (20 ticks = 1 second)
    interval: 100

  # Log types to disable
  disabled-log-types:
    - block_break
    - block_place

  # Webhook settings
  webhooks:
    #all_block_break:
    #  type: "block_break"
    #  url: "https://discord.com/api/webhooks/1234567890/abcdefghijklmnopqrstuvwxyz"
    #test_chat:
    #  type: "player_chat"
    #  url: "https://discord.com/api/webhooks/1234567890/abcdefghijklmnopqrstuvwxyz"
    #  condition: "message|=|test"

  # How many days to keep logs before they are automatically deleted.
  # Set to -1 to disable auto-deletion.
  retention-period-days: 30
  # How often (in minutes) the server should run the cleanup task to delete old logs.
  cleanup-interval-minutes: 60